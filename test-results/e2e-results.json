{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "dashboard/dashboard-google-auth.spec.ts", "file": "dashboard/dashboard-google-auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard E2E Tests with Google OAuth", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should show Google OAuth option", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 3314, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:39.093Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-dbbe094a546578da21a1", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 12, "column": 7}, {"title": "should redirect unauthenticated users to login", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 3425, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:39.103Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-1e0f66262645ea3aead8", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 22, "column": 7}, {"title": "should display login form elements", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 3373, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:39.101Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-7a4df45ee51d9b49ed2e", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 38, "column": 7}, {"title": "should handle invalid credentials gracefully", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 3174, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:39.076Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-607f8833510cbaa436a9", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 56, "column": 7}, {"title": "should work on mobile devices", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 3, "status": "failed", "duration": 2362, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:44.078Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-d388062b64c34157980c", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 76, "column": 7}, {"title": "should display registration link", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 2761, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:44.101Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-6c836d7eee74a9938aec", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 91, "column": 7}, {"title": "should display forgot password link", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 2, "status": "failed", "duration": 2895, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:44.097Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-99dde69ae6e1f6130f5b", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 107, "column": 7}, {"title": "should have proper page title and meta", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 2836, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:44.163Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-9b1bf33c3dccedd48c4d", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 123, "column": 7}, {"title": "should display proper error handling for network issues", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "failed", "duration": 1277, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:47.843Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-8c045ac68b0760b3ed3e", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 134, "column": 7}, {"title": "should validate form inputs", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 982, "error": {"message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n", "stack": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}, "message": "Error: expect.toBeVisible: Unexpected token \"=\" while parsing css selector \"h1, h2, text=Sign In\". Did you mean to CSS.escape it?\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for h1, h2, text=Sign In\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \n \u001b[90m  8 |\u001b[39m     \u001b[90m// Wait for login page to load\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1, h2, text=Sign In'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m\n \u001b[90m 12 |\u001b[39m   test(\u001b[32m'should show Google OAuth option'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:11:47.902Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts", "column": 56, "line": 9}}], "status": "unexpected"}], "id": "747ab274bcabdfc99376-4fc7d256d914bea42056", "file": "dashboard/dashboard-google-auth.spec.ts", "line": 161, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-25T19:11:37.731Z", "duration": 11801.729, "expected": 0, "skipped": 0, "unexpected": 10, "flaky": 0}}