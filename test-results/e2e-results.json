{"config": {"configFile": "/Users/<USER>/Desktop/adc/adc-account-web/playwright.config.ts", "rootDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/e2e-results.json"}], ["junit", {"outputFile": "test-results/e2e-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "/Users/<USER>/Desktop/adc/adc-account-web/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 4}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "/Users/<USER>/Desktop/adc/adc-account-web/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "dashboard/dashboard-auth-validation.spec.ts", "file": "dashboard/dashboard-auth-validation.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard Authentication Validation E2E Tests", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should redirect unauthenticated users to login", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 2874, "errors": [], "stdout": [{"text": "🔐 Testing unauthenticated access...\n"}, {"text": "✅ Unauthenticated users properly redirected to login\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:06.389Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-031e16be9ff8c6063273", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 18, "column": 7}, {"title": "should display login form elements correctly", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 2447, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:44:111", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 44}, "snippet": "\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Check for submit button\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ All login form elements are present'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 47 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 44}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Check for submit button\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ All login form elements are present'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 47 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:44:111\u001b[22m"}], "stdout": [{"text": "🔍 Testing login form elements...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:06.365Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 44}}], "status": "unexpected"}], "id": "b7c9bedf90c6a5d4e9b9-2e583d1a222532811339", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 34, "column": 7}, {"title": "should handle invalid credentials gracefully", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 5531, "errors": [], "stdout": [{"text": "🔐 Testing invalid credentials...\n"}, {"text": "✅ Invalid credentials handled correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:06.364Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-6981cb83f6222fe461b9", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 49, "column": 7}, {"title": "should attempt login with registered credentials", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 7181, "errors": [], "stdout": [{"text": "🔐 Testing login with registered credentials...\n"}, {"text": "Current URL after login attempt: http://localhost:3000/en/login\n"}, {"text": "ℹ️  Login attempt processed (may require verification)\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:06.362Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-aa69524335f301913ab3", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 69, "column": 7}, {"title": "should validate form inputs", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 3819, "errors": [], "stdout": [{"text": "📝 Testing form validation...\n"}, {"text": "✅ Form validation works correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:10.517Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-714e5a6cc9460bbccfa8", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 109, "column": 7}, {"title": "should work on mobile devices", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 2353, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:143:111", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 143}, "snippet": "\u001b[0m \u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"], input[name=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 143 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 144 |\u001b[39m\n \u001b[90m 145 |\u001b[39m     \u001b[90m// Try login on mobile\u001b[39m\n \u001b[90m 146 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mfill(testCredentials\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 143}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")') resolved to 2 elements:\n    1) <button data-slot=\"button\" class=\"inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })\n    2) <button type=\"submit\" data-slot=\"button\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")')\u001b[22m\n\n\n\u001b[0m \u001b[90m 141 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"password\"], input[name=\"password\"]'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 143 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'button[type=\"submit\"], button:has-text(\"Sign In\"), button:has-text(\"Login\")'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 144 |\u001b[39m\n \u001b[90m 145 |\u001b[39m     \u001b[90m// Try login on mobile\u001b[39m\n \u001b[90m 146 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m.\u001b[39mfill(testCredentials\u001b[33m.\u001b[39memail)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:143:111\u001b[22m"}], "stdout": [{"text": "📱 Testing mobile responsiveness...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:09.393Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 111, "line": 143}}], "status": "unexpected"}], "id": "b7c9bedf90c6a5d4e9b9-efe27634235ad3c12b2b", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 134, "column": 7}, {"title": "should display proper page title and meta", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 1542, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m\"\"\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m\"\"\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:162:19", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 19, "line": 162}, "snippet": "\u001b[0m \u001b[90m 160 |\u001b[39m     \u001b[90m// Check page title\u001b[39m\n \u001b[90m 161 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mtitle()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 162 |\u001b[39m     expect(title)\u001b[33m.\u001b[39mtoBeTruthy()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 163 |\u001b[39m     expect(title\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBe<PERSON>reater<PERSON><PERSON>(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 164 |\u001b[39m\n \u001b[90m 165 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✅ Page title: \"${title}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 19, "line": 162}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeTruthy\u001b[2m()\u001b[22m\n\nReceived: \u001b[31m\"\"\u001b[39m\n\n\u001b[0m \u001b[90m 160 |\u001b[39m     \u001b[90m// Check page title\u001b[39m\n \u001b[90m 161 |\u001b[39m     \u001b[36mconst\u001b[39m title \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mtitle()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 162 |\u001b[39m     expect(title)\u001b[33m.\u001b[39mtoBeTruthy()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 163 |\u001b[39m     expect(title\u001b[33m.\u001b[39mlength)\u001b[33m.\u001b[39mtoBeGreater<PERSON>han(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 164 |\u001b[39m\n \u001b[90m 165 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m`✅ Page title: \"${title}\"`\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:162:19\u001b[22m"}], "stdout": [{"text": "🔍 Testing page metadata...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:13.598Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 19, "line": 162}}], "status": "unexpected"}], "id": "b7c9bedf90c6a5d4e9b9-73da53d0ca3886b1f909", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 157, "column": 7}, {"title": "should handle network errors gracefully", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 5179, "error": {"message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m", "stack": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:190:28", "location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 28, "line": 190}, "snippet": "\u001b[0m \u001b[90m 188 |\u001b[39m     \u001b[90m// Should still be on login page\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m isStillOnLogin \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     expect(isStillOnLogin)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m\n \u001b[90m 192 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Network error handling works'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 193 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 28, "line": 190}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32mtrue\u001b[39m\nReceived: \u001b[31mfalse\u001b[39m\n\n\u001b[0m \u001b[90m 188 |\u001b[39m     \u001b[90m// Should still be on login page\u001b[39m\n \u001b[90m 189 |\u001b[39m     \u001b[36mconst\u001b[39m isStillOnLogin \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[type=\"email\"], input[name=\"email\"]'\u001b[39m)\u001b[33m.\u001b[39misVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 190 |\u001b[39m     expect(isStillOnLogin)\u001b[33m.\u001b[39mtoBe(\u001b[36mtrue\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 191 |\u001b[39m\n \u001b[90m 192 |\u001b[39m     console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Network error handling works'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 193 |\u001b[39m   })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:190:28\u001b[22m"}], "stdout": [{"text": "🌐 Testing network error handling...\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:12.076Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Desktop/adc/adc-account-web/test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts", "column": 28, "line": 190}}], "status": "unexpected"}], "id": "b7c9bedf90c6a5d4e9b9-c13e5c0d2ee22aa738f7", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 168, "column": 7}, {"title": "should navigate to registration page", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 3224, "errors": [], "stdout": [{"text": "🔄 Testing navigation to registration...\n"}, {"text": "✅ Registration link works correctly\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:13.759Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-6282d172aa7d618f48ee", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 195, "column": 7}, {"title": "should take screenshot for visual verification", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 1170, "errors": [], "stdout": [{"text": "📸 Taking screenshot for visual verification...\n"}, {"text": "✅ Screenshots taken for verification\n"}], "stderr": [], "retry": 0, "startTime": "2025-05-25T19:20:14.973Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b7c9bedf90c6a5d4e9b9-51d4863dd8283cd18414", "file": "dashboard/dashboard-auth-validation.spec.ts", "line": 214, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-25T19:20:04.614Z", "duration": 12684.141, "expected": 6, "skipped": 0, "unexpected": 4, "flaky": 0}}