<testsuites id="" name="" tests="10" failures="10" skipped="0" errors="0" time="11.801729">
<testsuite name="dashboard/dashboard-google-auth.spec.ts" timestamp="2025-05-25T19:11:38.666Z" hostname="chromium" tests="10" failures="10" skipped="0" time="26.399" errors="0">
<testcase name="Dashboard E2E Tests with Google OAuth › should show Google OAuth option" classname="dashboard/dashboard-google-auth.spec.ts" time="3.314">
<failure message="dashboard-google-auth.spec.ts:12:7 should show Google OAuth option" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:12:7 › Dashboard E2E Tests with Google OAuth › should show Google OAuth option 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-b46f1-ld-show-Google-OAuth-option-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should redirect unauthenticated users to login" classname="dashboard/dashboard-google-auth.spec.ts" time="3.425">
<failure message="dashboard-google-auth.spec.ts:22:7 should redirect unauthenticated users to login" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:22:7 › Dashboard E2E Tests with Google OAuth › should redirect unauthenticated users to login 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-2be65-uthenticated-users-to-login-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should display login form elements" classname="dashboard/dashboard-google-auth.spec.ts" time="3.373">
<failure message="dashboard-google-auth.spec.ts:38:7 should display login form elements" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:38:7 › Dashboard E2E Tests with Google OAuth › should display login form elements 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-eedb7-display-login-form-elements-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should handle invalid credentials gracefully" classname="dashboard/dashboard-google-auth.spec.ts" time="3.174">
<failure message="dashboard-google-auth.spec.ts:56:7 should handle invalid credentials gracefully" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:56:7 › Dashboard E2E Tests with Google OAuth › should handle invalid credentials gracefully 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-4f7f1-alid-credentials-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should work on mobile devices" classname="dashboard/dashboard-google-auth.spec.ts" time="2.362">
<failure message="dashboard-google-auth.spec.ts:76:7 should work on mobile devices" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:76:7 › Dashboard E2E Tests with Google OAuth › should work on mobile devices 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-18561-ould-work-on-mobile-devices-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should display registration link" classname="dashboard/dashboard-google-auth.spec.ts" time="2.761">
<failure message="dashboard-google-auth.spec.ts:91:7 should display registration link" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:91:7 › Dashboard E2E Tests with Google OAuth › should display registration link 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-ec8b0-d-display-registration-link-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should display forgot password link" classname="dashboard/dashboard-google-auth.spec.ts" time="2.895">
<failure message="dashboard-google-auth.spec.ts:107:7 should display forgot password link" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:107:7 › Dashboard E2E Tests with Google OAuth › should display forgot password link 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-07075-isplay-forgot-password-link-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should have proper page title and meta" classname="dashboard/dashboard-google-auth.spec.ts" time="2.836">
<failure message="dashboard-google-auth.spec.ts:123:7 should have proper page title and meta" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:123:7 › Dashboard E2E Tests with Google OAuth › should have proper page title and meta 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-09faa--proper-page-title-and-meta-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should display proper error handling for network issues" classname="dashboard/dashboard-google-auth.spec.ts" time="1.277">
<failure message="dashboard-google-auth.spec.ts:134:7 should display proper error handling for network issues" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:134:7 › Dashboard E2E Tests with Google OAuth › should display proper error handling for network issues 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-c8e94-handling-for-network-issues-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard E2E Tests with Google OAuth › should validate form inputs" classname="dashboard/dashboard-google-auth.spec.ts" time="0.982">
<failure message="dashboard-google-auth.spec.ts:161:7 should validate form inputs" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-google-auth.spec.ts:161:7 › Dashboard E2E Tests with Google OAuth › should validate form inputs 

    Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for h1, h2, text=Sign In


       7 |     
       8 |     // Wait for login page to load
    >  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
         |                                                        ^
      10 |   });
      11 |
      12 |   test('should show Google OAuth option', async ({ page }) => {
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-google-f0499-should-validate-form-inputs-chromium/error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>