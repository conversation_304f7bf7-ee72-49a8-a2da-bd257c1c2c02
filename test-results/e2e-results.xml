<testsuites id="" name="" tests="10" failures="4" skipped="0" errors="0" time="12.684141">
<testsuite name="dashboard/dashboard-auth-validation.spec.ts" timestamp="2025-05-25T19:20:05.627Z" hostname="chromium" tests="10" failures="4" skipped="0" time="35.32" errors="0">
<testcase name="Dashboard Authentication Validation E2E Tests › should redirect unauthenticated users to login" classname="dashboard/dashboard-auth-validation.spec.ts" time="2.874">
<system-out>
<![CDATA[🔐 Testing unauthenticated access...
✅ Unauthenticated users properly redirected to login
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should display login form elements correctly" classname="dashboard/dashboard-auth-validation.spec.ts" time="2.447">
<failure message="dashboard-auth-validation.spec.ts:34:7 should display login form elements correctly" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-auth-validation.spec.ts:34:7 › Dashboard Authentication Validation E2E Tests › should display login form elements correctly 

    Error: expect.toBeVisible: Error: strict mode violation: locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")') resolved to 2 elements:
        1) <button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })
        2) <button type="submit" data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')


      42 |
      43 |     // Check for submit button
    > 44 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
         |                                                                                                               ^
      45 |
      46 |     console.log('✅ All login form elements are present');
      47 |   });
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:44:111

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔍 Testing login form elements...

[[ATTACHMENT|dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-auth-v-f142d-gin-form-elements-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should handle invalid credentials gracefully" classname="dashboard/dashboard-auth-validation.spec.ts" time="5.531">
<system-out>
<![CDATA[🔐 Testing invalid credentials...
✅ Invalid credentials handled correctly
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should attempt login with registered credentials" classname="dashboard/dashboard-auth-validation.spec.ts" time="7.181">
<system-out>
<![CDATA[🔐 Testing login with registered credentials...
Current URL after login attempt: http://localhost:3000/en/login
ℹ️  Login attempt processed (may require verification)
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should validate form inputs" classname="dashboard/dashboard-auth-validation.spec.ts" time="3.819">
<system-out>
<![CDATA[📝 Testing form validation...
✅ Form validation works correctly
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should work on mobile devices" classname="dashboard/dashboard-auth-validation.spec.ts" time="2.353">
<failure message="dashboard-auth-validation.spec.ts:134:7 should work on mobile devices" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-auth-validation.spec.ts:134:7 › Dashboard Authentication Validation E2E Tests › should work on mobile devices 

    Error: expect.toBeVisible: Error: strict mode violation: locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")') resolved to 2 elements:
        1) <button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })
        2) <button type="submit" data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')


      141 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
      142 |     await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
    > 143 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
          |                                                                                                               ^
      144 |
      145 |     // Try login on mobile
      146 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:143:111

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[📱 Testing mobile responsiveness...

[[ATTACHMENT|dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-auth-v-82dea-ould-work-on-mobile-devices-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should display proper page title and meta" classname="dashboard/dashboard-auth-validation.spec.ts" time="1.542">
<failure message="dashboard-auth-validation.spec.ts:157:7 should display proper page title and meta" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-auth-validation.spec.ts:157:7 › Dashboard Authentication Validation E2E Tests › should display proper page title and meta 

    Error: expect(received).toBeTruthy()

    Received: ""

      160 |     // Check page title
      161 |     const title = await page.title();
    > 162 |     expect(title).toBeTruthy();
          |                   ^
      163 |     expect(title.length).toBeGreaterThan(0);
      164 |
      165 |     console.log(`✅ Page title: "${title}"`);
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:162:19

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🔍 Testing page metadata...

[[ATTACHMENT|dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-auth-v-9a187--proper-page-title-and-meta-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should handle network errors gracefully" classname="dashboard/dashboard-auth-validation.spec.ts" time="5.179">
<failure message="dashboard-auth-validation.spec.ts:168:7 should handle network errors gracefully" type="FAILURE">
<![CDATA[  [chromium] › dashboard/dashboard-auth-validation.spec.ts:168:7 › Dashboard Authentication Validation E2E Tests › should handle network errors gracefully 

    Error: expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      188 |     // Should still be on login page
      189 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    > 190 |     expect(isStillOnLogin).toBe(true);
          |                            ^
      191 |
      192 |     console.log('✅ Network error handling works');
      193 |   });
        at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:190:28

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../test-results/dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[🌐 Testing network error handling...

[[ATTACHMENT|dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/test-failed-1.png]]

[[ATTACHMENT|dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/video.webm]]

[[ATTACHMENT|dashboard-dashboard-auth-v-e3293-e-network-errors-gracefully-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should navigate to registration page" classname="dashboard/dashboard-auth-validation.spec.ts" time="3.224">
<system-out>
<![CDATA[🔄 Testing navigation to registration...
✅ Registration link works correctly
]]>
</system-out>
</testcase>
<testcase name="Dashboard Authentication Validation E2E Tests › should take screenshot for visual verification" classname="dashboard/dashboard-auth-validation.spec.ts" time="1.17">
<system-out>
<![CDATA[📸 Taking screenshot for visual verification...
✅ Screenshots taken for verification
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>