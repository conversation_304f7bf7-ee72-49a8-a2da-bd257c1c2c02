# Test info

- Name: Dashboard Authentication Validation E2E Tests >> should work on mobile devices
- Location: /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:134:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")') resolved to 2 elements:
    1) <button data-slot="button" class="inline-flex items-center justify-center whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive hover:bg-accent hover:text-accent-foreground …>…</button> aka getByRole('button', { name: 'Login' })
    2) <button type="submit" data-slot="button" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive bg-primary tex…>Sign In</button> aka getByRole('button', { name: 'Sign In' })

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')

    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:143:111
```

# Page snapshot

```yaml
- banner:
  - button "Toggle sidebar"
  - link "ADC Account":
    - /url: /
  - button "Toggle theme"
  - button "🇺🇸"
  - button "Login"
- complementary:
  - link "ADC Account":
    - /url: /en
  - heading "Overview" [level=3]
  - link "Dashboard":
    - /url: /en/dashboard
    - button "Dashboard"
  - heading "Financial Management" [level=3]
  - link "Chart of Accounts":
    - /url: /en/chart-of-accounts
    - button "Chart of Accounts"
  - link "Journal Entries":
    - /url: /en/journal-entries
    - button "Journal Entries"
  - link "Banking":
    - /url: /en/banking
    - button "Banking"
  - link "Assets":
    - /url: /en/assets
    - button "Assets"
  - heading "Sales & Purchases" [level=3]
  - link "Customers":
    - /url: /en/customers
    - button "Customers"
  - link "Invoices":
    - /url: /en/invoices
    - button "Invoices"
  - link "Vendors":
    - /url: /en/vendors
    - button "Vendors"
  - link "Bills":
    - /url: /en/bills
    - button "Bills"
  - link "Expenses":
    - /url: /en/expenses
    - button "Expenses"
  - heading "Other" [level=3]
  - link "Inventory":
    - /url: /en/inventory
    - button "Inventory"
  - link "Budget":
    - /url: /en/budget
    - button "Budget"
  - link "Cash Flow":
    - /url: /en/cashflow
    - button "Cash Flow"
  - link "Taxes":
    - /url: /en/taxes
    - button "Taxes"
  - link "Payroll":
    - /url: /en/payroll
    - button "Payroll"
  - heading "Reports" [level=3]
  - link "Reports":
    - /url: /en/reports
    - button "Reports"
  - link "Custom Reports":
    - /url: /en/reports/custom
    - button "Custom Reports"
  - link "A/R Aging":
    - /url: /en/reports/accounts-receivable-aging
    - button "A/R Aging"
  - link "Tax Reports":
    - /url: /en/taxes/reports
    - button "Tax Reports"
  - heading "System" [level=3]
  - link "Settings":
    - /url: /en/settings
    - button "Settings"
  - link "sidebar.links.organizations":
    - /url: /en/organizations
    - button "sidebar.links.organizations"
  - link "sidebar.links.branches":
    - /url: /en/branches
    - button "sidebar.links.branches"
  - link "Merchants":
    - /url: /en/settings/merchants
    - button "Merchants"
  - link "Integrations":
    - /url: /en/integrations
    - button "Integrations"
  - link "User Management":
    - /url: /en/users
    - button "User Management"
  - link "Pricing & Plans":
    - /url: /en/pricing
    - button "Pricing & Plans"
- main:
  - text: Sign In Use Google OAuth for quick access, or enter your existing account credentials Email
  - textbox "Email"
  - text: Password
  - link "Forgot password?":
    - /url: /en/forgot-password
  - textbox "Password"
  - button "Sign In"
  - text: Or continue with
  - button "Continue with Google (Recommended)":
    - img
    - text: Continue with Google (Recommended)
  - text: "💡 New users: Use Google login above. Email/password requires an existing account. Don't have an account?"
  - link "Sign up":
    - /url: /en/register
- contentinfo:
  - paragraph: © 2025 ADC Account. All rights reserved.
  - navigation:
    - link "Terms of Service":
      - /url: /en/terms
    - link "Privacy Policy":
      - /url: /en/privacy
    - link "Help":
      - /url: /en/help
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- button "Open issues overlay": 2 Issue
- button "Collapse issues badge":
  - img
- alert
```

# Test source

```ts
   43 |     // Check for submit button
   44 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
   45 |
   46 |     console.log('✅ All login form elements are present');
   47 |   });
   48 |
   49 |   test('should handle invalid credentials gracefully', async ({ page }) => {
   50 |     console.log('🔐 Testing invalid credentials...');
   51 |
   52 |     // Try with invalid credentials
   53 |     await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
   54 |     await page.locator('input[type="password"], input[name="password"]').first().fill('wrongpassword');
   55 |
   56 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
   57 |     await submitButton.click();
   58 |
   59 |     // Wait for response
   60 |     await page.waitForTimeout(3000);
   61 |
   62 |     // Should stay on login page (authentication failed)
   63 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
   64 |     expect(isStillOnLogin).toBe(true);
   65 |
   66 |     console.log('✅ Invalid credentials handled correctly');
   67 |   });
   68 |
   69 |   test('should attempt login with registered credentials', async ({ page }) => {
   70 |     console.log('🔐 Testing login with registered credentials...');
   71 |
   72 |     // Fill in the credentials we registered
   73 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
   74 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
   75 |
   76 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
   77 |     await submitButton.click();
   78 |
   79 |     // Wait for response
   80 |     await page.waitForTimeout(5000);
   81 |
   82 |     // Check what happened after login attempt
   83 |     const currentUrl = page.url();
   84 |     console.log(`Current URL after login attempt: ${currentUrl}`);
   85 |
   86 |     // Since the user needs email verification, we expect to either:
   87 |     // 1. Stay on login page with a message
   88 |     // 2. Be redirected to a verification page
   89 |     // 3. Get an error about email verification
   90 |
   91 |     const pageText = await page.textContent('body');
   92 |     const hasVerificationMessage = pageText?.includes('verify') ||
   93 |                                   pageText?.includes('email') ||
   94 |                                   pageText?.includes('confirm') ||
   95 |                                   pageText?.includes('check');
   96 |
   97 |     if (hasVerificationMessage) {
   98 |       console.log('✅ Email verification required (expected behavior)');
   99 |     } else if (currentUrl.includes('dashboard')) {
  100 |       console.log('✅ Login successful - redirected to dashboard');
  101 |     } else {
  102 |       console.log('ℹ️  Login attempt processed (may require verification)');
  103 |     }
  104 |
  105 |     // The test passes if the system handles the login attempt gracefully
  106 |     expect(true).toBe(true);
  107 |   });
  108 |
  109 |   test('should validate form inputs', async ({ page }) => {
  110 |     console.log('📝 Testing form validation...');
  111 |
  112 |     // Try to submit empty form
  113 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  114 |     await submitButton.click();
  115 |
  116 |     // Check if form validation prevents submission
  117 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
  118 |     expect(isStillOnLogin).toBe(true);
  119 |
  120 |     // Try with invalid email format
  121 |     await page.locator('input[type="email"], input[name="email"]').first().fill('invalid-email');
  122 |     await page.locator('input[type="password"], input[name="password"]').first().fill('password');
  123 |     await submitButton.click();
  124 |
  125 |     await page.waitForTimeout(2000);
  126 |
  127 |     // Should still be on login page due to validation
  128 |     const stillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
  129 |     expect(stillOnLogin).toBe(true);
  130 |
  131 |     console.log('✅ Form validation works correctly');
  132 |   });
  133 |
  134 |   test('should work on mobile devices', async ({ page }) => {
  135 |     console.log('📱 Testing mobile responsiveness...');
  136 |
  137 |     // Set mobile viewport
  138 |     await page.setViewportSize({ width: 375, height: 667 });
  139 |
  140 |     // Verify login form works on mobile
  141 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
  142 |     await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
> 143 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
      |                                                                                                               ^ Error: expect.toBeVisible: Error: strict mode violation: locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")') resolved to 2 elements:
  144 |
  145 |     // Try login on mobile
  146 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  147 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  148 |
  149 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  150 |     await submitButton.click();
  151 |
  152 |     await page.waitForTimeout(3000);
  153 |
  154 |     console.log('✅ Login form works on mobile');
  155 |   });
  156 |
  157 |   test('should display proper page title and meta', async ({ page }) => {
  158 |     console.log('🔍 Testing page metadata...');
  159 |
  160 |     // Check page title
  161 |     const title = await page.title();
  162 |     expect(title).toBeTruthy();
  163 |     expect(title.length).toBeGreaterThan(0);
  164 |
  165 |     console.log(`✅ Page title: "${title}"`);
  166 |   });
  167 |
  168 |   test('should handle network errors gracefully', async ({ page }) => {
  169 |     console.log('🌐 Testing network error handling...');
  170 |
  171 |     // Fill in credentials
  172 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  173 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  174 |
  175 |     // Simulate network failure by going offline
  176 |     await page.context().setOffline(true);
  177 |
  178 |     // Try to submit
  179 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  180 |     await submitButton.click();
  181 |
  182 |     // Wait for potential error handling
  183 |     await page.waitForTimeout(3000);
  184 |
  185 |     // Go back online
  186 |     await page.context().setOffline(false);
  187 |
  188 |     // Should still be on login page
  189 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
  190 |     expect(isStillOnLogin).toBe(true);
  191 |
  192 |     console.log('✅ Network error handling works');
  193 |   });
  194 |
  195 |   test('should navigate to registration page', async ({ page }) => {
  196 |     console.log('🔄 Testing navigation to registration...');
  197 |
  198 |     // Find and click registration link
  199 |     const registerLink = page.locator('a:has-text("Sign up"), a:has-text("Register"), a:has-text("Create account")');
  200 |
  201 |     const linkExists = await registerLink.isVisible();
  202 |     if (linkExists) {
  203 |       await registerLink.click();
  204 |
  205 |       // Should navigate to registration page
  206 |       await expect(page).toHaveURL(/.*register.*/);
  207 |
  208 |       console.log('✅ Registration link works correctly');
  209 |     } else {
  210 |       console.log('ℹ️  Registration link not found on login page');
  211 |     }
  212 |   });
  213 |
  214 |   test('should take screenshot for visual verification', async ({ page }) => {
  215 |     console.log('📸 Taking screenshot for visual verification...');
  216 |
  217 |     // Take a screenshot of the login page
  218 |     await page.screenshot({
  219 |       path: 'test-results/login-page-verification.png',
  220 |       fullPage: true
  221 |     });
  222 |
  223 |     // Fill in credentials and take another screenshot
  224 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  225 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  226 |
  227 |     await page.screenshot({
  228 |       path: 'test-results/login-form-filled.png',
  229 |       fullPage: true
  230 |     });
  231 |
  232 |     console.log('✅ Screenshots taken for verification');
  233 |   });
  234 | });
  235 |
```