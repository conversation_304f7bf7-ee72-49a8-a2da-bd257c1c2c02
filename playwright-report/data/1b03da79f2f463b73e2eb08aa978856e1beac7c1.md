# Test info

- Name: Dashboard E2E Tests with Google OAuth >> should display forgot password link
- Location: /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:107:7

# Error details

```
Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for h1, h2, text=Sign In

    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-google-auth.spec.ts:9:56
```

# Page snapshot

```yaml
- paragraph: Loading...
- region "Notifications alt+T"
- region "Notifications (F8)":
  - list
- button "Open Next.js Dev Tools":
  - img
- alert
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Dashboard E2E Tests with Google OAuth', () => {
   4 |   test.beforeEach(async ({ page }) => {
   5 |     // Navigate to login page first
   6 |     await page.goto('/en/login');
   7 |     
   8 |     // Wait for login page to load
>  9 |     await expect(page.locator('h1, h2, text=Sign In')).toBeVisible({ timeout: 10000 });
     |                                                        ^ Error: expect.toBeVisible: Unexpected token "=" while parsing css selector "h1, h2, text=Sign In". Did you mean to CSS.escape it?
   10 |   });
   11 |
   12 |   test('should show Google OAuth option', async ({ page }) => {
   13 |     console.log('🔍 Checking for Google OAuth option...');
   14 |     
   15 |     // Check for Google OAuth button
   16 |     const googleButton = page.locator('button:has-text("Google"), button:has-text("Continue with Google")');
   17 |     await expect(googleButton).toBeVisible({ timeout: 10000 });
   18 |     
   19 |     console.log('✅ Google OAuth button found');
   20 |   });
   21 |
   22 |   test('should redirect unauthenticated users to login', async ({ page }) => {
   23 |     console.log('🔐 Testing unauthenticated access...');
   24 |     
   25 |     // Try to access dashboard directly without authentication
   26 |     await page.goto('/en/dashboard');
   27 |     
   28 |     // Should be redirected to login page
   29 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible({ timeout: 10000 });
   30 |     
   31 |     // Verify we're on login page
   32 |     const currentUrl = page.url();
   33 |     expect(currentUrl).toContain('login');
   34 |     
   35 |     console.log('✅ Unauthenticated users properly redirected to login');
   36 |   });
   37 |
   38 |   test('should display login form elements', async ({ page }) => {
   39 |     console.log('🔍 Testing login form elements...');
   40 |     
   41 |     // Check for email input
   42 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
   43 |     
   44 |     // Check for password input
   45 |     await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
   46 |     
   47 |     // Check for submit button
   48 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
   49 |     
   50 |     // Check for Google OAuth button
   51 |     await expect(page.locator('button:has-text("Google"), button:has-text("Continue with Google")')).toBeVisible();
   52 |     
   53 |     console.log('✅ All login form elements are present');
   54 |   });
   55 |
   56 |   test('should handle invalid credentials gracefully', async ({ page }) => {
   57 |     console.log('🔐 Testing invalid credentials...');
   58 |     
   59 |     // Try with invalid credentials
   60 |     await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
   61 |     await page.locator('input[type="password"], input[name="password"]').first().fill('wrongpassword');
   62 |     
   63 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
   64 |     await submitButton.click();
   65 |     
   66 |     // Wait for response
   67 |     await page.waitForTimeout(3000);
   68 |     
   69 |     // Should stay on login page (authentication failed)
   70 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
   71 |     expect(isStillOnLogin).toBe(true);
   72 |     
   73 |     console.log('✅ Invalid credentials handled correctly');
   74 |   });
   75 |
   76 |   test('should work on mobile devices', async ({ page }) => {
   77 |     console.log('📱 Testing mobile responsiveness...');
   78 |     
   79 |     // Set mobile viewport
   80 |     await page.setViewportSize({ width: 375, height: 667 });
   81 |     
   82 |     // Verify login form works on mobile
   83 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
   84 |     await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
   85 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
   86 |     await expect(page.locator('button:has-text("Google"), button:has-text("Continue with Google")')).toBeVisible();
   87 |     
   88 |     console.log('✅ Login form works on mobile');
   89 |   });
   90 |
   91 |   test('should display registration link', async ({ page }) => {
   92 |     console.log('🔍 Testing registration link...');
   93 |     
   94 |     // Check for registration link
   95 |     const registerLink = page.locator('a:has-text("Sign up"), a:has-text("Register"), a:has-text("Create account")');
   96 |     await expect(registerLink).toBeVisible({ timeout: 10000 });
   97 |     
   98 |     // Click registration link
   99 |     await registerLink.click();
  100 |     
  101 |     // Should navigate to registration page
  102 |     await expect(page).toHaveURL(/.*register.*/);
  103 |     
  104 |     console.log('✅ Registration link works correctly');
  105 |   });
  106 |
  107 |   test('should display forgot password link', async ({ page }) => {
  108 |     console.log('🔍 Testing forgot password link...');
  109 |     
```