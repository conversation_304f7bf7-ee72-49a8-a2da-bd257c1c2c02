# Test info

- Name: Dashboard Authentication Validation E2E Tests >> should handle network errors gracefully
- Location: /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:168:7

# Error details

```
Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at /Users/<USER>/Desktop/adc/adc-account-web/e2e/dashboard/dashboard-auth-validation.spec.ts:190:28
```

# Test source

```ts
   90 |
   91 |     const pageText = await page.textContent('body');
   92 |     const hasVerificationMessage = pageText?.includes('verify') ||
   93 |                                   pageText?.includes('email') ||
   94 |                                   pageText?.includes('confirm') ||
   95 |                                   pageText?.includes('check');
   96 |
   97 |     if (hasVerificationMessage) {
   98 |       console.log('✅ Email verification required (expected behavior)');
   99 |     } else if (currentUrl.includes('dashboard')) {
  100 |       console.log('✅ Login successful - redirected to dashboard');
  101 |     } else {
  102 |       console.log('ℹ️  Login attempt processed (may require verification)');
  103 |     }
  104 |
  105 |     // The test passes if the system handles the login attempt gracefully
  106 |     expect(true).toBe(true);
  107 |   });
  108 |
  109 |   test('should validate form inputs', async ({ page }) => {
  110 |     console.log('📝 Testing form validation...');
  111 |
  112 |     // Try to submit empty form
  113 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  114 |     await submitButton.click();
  115 |
  116 |     // Check if form validation prevents submission
  117 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
  118 |     expect(isStillOnLogin).toBe(true);
  119 |
  120 |     // Try with invalid email format
  121 |     await page.locator('input[type="email"], input[name="email"]').first().fill('invalid-email');
  122 |     await page.locator('input[type="password"], input[name="password"]').first().fill('password');
  123 |     await submitButton.click();
  124 |
  125 |     await page.waitForTimeout(2000);
  126 |
  127 |     // Should still be on login page due to validation
  128 |     const stillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
  129 |     expect(stillOnLogin).toBe(true);
  130 |
  131 |     console.log('✅ Form validation works correctly');
  132 |   });
  133 |
  134 |   test('should work on mobile devices', async ({ page }) => {
  135 |     console.log('📱 Testing mobile responsiveness...');
  136 |
  137 |     // Set mobile viewport
  138 |     await page.setViewportSize({ width: 375, height: 667 });
  139 |
  140 |     // Verify login form works on mobile
  141 |     await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
  142 |     await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
  143 |     await expect(page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")')).toBeVisible();
  144 |
  145 |     // Try login on mobile
  146 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  147 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  148 |
  149 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  150 |     await submitButton.click();
  151 |
  152 |     await page.waitForTimeout(3000);
  153 |
  154 |     console.log('✅ Login form works on mobile');
  155 |   });
  156 |
  157 |   test('should display proper page title and meta', async ({ page }) => {
  158 |     console.log('🔍 Testing page metadata...');
  159 |
  160 |     // Check page title
  161 |     const title = await page.title();
  162 |     expect(title).toBeTruthy();
  163 |     expect(title.length).toBeGreaterThan(0);
  164 |
  165 |     console.log(`✅ Page title: "${title}"`);
  166 |   });
  167 |
  168 |   test('should handle network errors gracefully', async ({ page }) => {
  169 |     console.log('🌐 Testing network error handling...');
  170 |
  171 |     // Fill in credentials
  172 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  173 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  174 |
  175 |     // Simulate network failure by going offline
  176 |     await page.context().setOffline(true);
  177 |
  178 |     // Try to submit
  179 |     const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
  180 |     await submitButton.click();
  181 |
  182 |     // Wait for potential error handling
  183 |     await page.waitForTimeout(3000);
  184 |
  185 |     // Go back online
  186 |     await page.context().setOffline(false);
  187 |
  188 |     // Should still be on login page
  189 |     const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
> 190 |     expect(isStillOnLogin).toBe(true);
      |                            ^ Error: expect(received).toBe(expected) // Object.is equality
  191 |
  192 |     console.log('✅ Network error handling works');
  193 |   });
  194 |
  195 |   test('should navigate to registration page', async ({ page }) => {
  196 |     console.log('🔄 Testing navigation to registration...');
  197 |
  198 |     // Find and click registration link
  199 |     const registerLink = page.locator('a:has-text("Sign up"), a:has-text("Register"), a:has-text("Create account")');
  200 |
  201 |     const linkExists = await registerLink.isVisible();
  202 |     if (linkExists) {
  203 |       await registerLink.click();
  204 |
  205 |       // Should navigate to registration page
  206 |       await expect(page).toHaveURL(/.*register.*/);
  207 |
  208 |       console.log('✅ Registration link works correctly');
  209 |     } else {
  210 |       console.log('ℹ️  Registration link not found on login page');
  211 |     }
  212 |   });
  213 |
  214 |   test('should take screenshot for visual verification', async ({ page }) => {
  215 |     console.log('📸 Taking screenshot for visual verification...');
  216 |
  217 |     // Take a screenshot of the login page
  218 |     await page.screenshot({
  219 |       path: 'test-results/login-page-verification.png',
  220 |       fullPage: true
  221 |     });
  222 |
  223 |     // Fill in credentials and take another screenshot
  224 |     await page.locator('input[type="email"], input[name="email"]').first().fill(testCredentials.email);
  225 |     await page.locator('input[type="password"], input[name="password"]').first().fill(testCredentials.password);
  226 |
  227 |     await page.screenshot({
  228 |       path: 'test-results/login-form-filled.png',
  229 |       fullPage: true
  230 |     });
  231 |
  232 |     console.log('✅ Screenshots taken for verification');
  233 |   });
  234 | });
  235 |
```