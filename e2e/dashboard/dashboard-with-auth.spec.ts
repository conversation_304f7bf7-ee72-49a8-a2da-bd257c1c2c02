import { test, expect } from '@playwright/test';

test.describe('Dashboard E2E Tests with Authentication', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page first
    await page.goto('/en/login');

    // Wait for login page to load
    await expect(page.locator('h1, h2')).toContainText(/sign in|login/i, { timeout: 10000 });
  });

  test('should complete login flow and access dashboard', async ({ page }) => {
    console.log('🔐 Testing login flow...');

    // Check if we're on the login page
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible({ timeout: 10000 });

    // Fill in login credentials with real test account
    const emailInput = page.locator('input[type="email"], input[name="email"]').first();
    const passwordInput = page.locator('input[type="password"], input[name="password"]').first();

    await emailInput.fill('<EMAIL>');
    await passwordInput.fill('TestPass123!');

    // Find and click the submit button
    const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await expect(submitButton).toBeVisible();

    // Click submit and wait for navigation
    await Promise.all([
      page.waitForURL('**/dashboard**', { timeout: 30000 }),
      submitButton.click(),
    ]);

    console.log('✅ Login successful, checking dashboard...');

    // Now we should be on the dashboard
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    console.log('✅ Dashboard loaded successfully');
  });

  test('should display dashboard elements after authentication', async ({ page }) => {
    // First complete login
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('TestPass123!');

    await Promise.all([
      page.waitForURL('**/dashboard**', { timeout: 30000 }),
      page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first().click(),
    ]);

    // Verify dashboard elements
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    // Check for financial summary cards
    const financialTexts = ['totalRevenue', 'totalExpenses', 'netIncome', 'cashBalance'];
    let foundTexts = 0;

    for (const text of financialTexts) {
      try {
        await expect(page.locator(`text=${text}`)).toBeVisible({ timeout: 5000 });
        foundTexts++;
        console.log(`✅ Found: ${text}`);
      } catch {
        console.log(`⚠️  Not found: ${text}`);
      }
    }

    expect(foundTexts).toBeGreaterThan(0);
    console.log(`✅ Found ${foundTexts} financial elements`);
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    console.log('🔐 Testing invalid credentials...');

    // Try with invalid credentials
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('wrongpassword');

    const submitButton = page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first();
    await submitButton.click();

    // Should stay on login page or show error
    await page.waitForTimeout(3000);

    // Check if we're still on login page (authentication failed)
    const isStillOnLogin = await page.locator('input[type="email"], input[name="email"]').isVisible();
    expect(isStillOnLogin).toBe(true);

    console.log('✅ Invalid credentials handled correctly');
  });

  test('should redirect unauthenticated users to login', async ({ page }) => {
    console.log('🔐 Testing unauthenticated access...');

    // Try to access dashboard directly without authentication
    await page.goto('/en/dashboard');

    // Should be redirected to login page
    await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible({ timeout: 10000 });

    // Verify we're on login page
    const currentUrl = page.url();
    expect(currentUrl).toContain('login');

    console.log('✅ Unauthenticated users properly redirected to login');
  });

  test('should maintain session after page refresh', async ({ page }) => {
    console.log('🔐 Testing session persistence...');

    // Complete login first
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('TestPass123!');

    await Promise.all([
      page.waitForURL('**/dashboard**', { timeout: 30000 }),
      page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first().click(),
    ]);

    // Verify we're on dashboard
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    // Refresh the page
    await page.reload();

    // Should still be on dashboard (session maintained)
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    console.log('✅ Session maintained after refresh');
  });

  test('should work on mobile devices after authentication', async ({ page }) => {
    console.log('📱 Testing mobile responsiveness with auth...');

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Complete login on mobile
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('TestPass123!');

    await Promise.all([
      page.waitForURL('**/dashboard**', { timeout: 30000 }),
      page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first().click(),
    ]);

    // Verify dashboard works on mobile
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    // Check that elements are still visible on mobile
    await expect(page.locator('h1')).toBeVisible();

    console.log('✅ Dashboard works on mobile after authentication');
  });

  test('should display actual dashboard data after authentication', async ({ page }) => {
    console.log('📊 Testing dashboard data display...');

    // Complete login
    await page.locator('input[type="email"], input[name="email"]').first().fill('<EMAIL>');
    await page.locator('input[type="password"], input[name="password"]').first().fill('TestPass123!');

    await Promise.all([
      page.waitForURL('**/dashboard**', { timeout: 30000 }),
      page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').first().click(),
    ]);

    // Wait for dashboard to fully load
    await expect(page.locator('h1')).toContainText('Dashboard', { timeout: 15000 });

    // Wait for any loading states to complete
    await page.waitForTimeout(3000);

    // Check for various dashboard elements
    const elementsToCheck = [
      'h1', // Main heading
      'button', // Should have buttons
      'div', // Should have content divs
    ];

    for (const element of elementsToCheck) {
      const count = await page.locator(element).count();
      expect(count).toBeGreaterThan(0);
      console.log(`✅ Found ${count} ${element} elements`);
    }

    // Take a screenshot for verification
    await page.screenshot({
      path: 'test-results/dashboard-authenticated.png',
      fullPage: true
    });

    console.log('✅ Dashboard data displayed successfully');
  });
});
